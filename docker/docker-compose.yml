version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: edu-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: edu_user
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - edu-network

  # Nacos服务注册与配置中心
  nacos:
    image: nacos/nacos-server:v2.2.0
    container_name: edu-nacos
    restart: always
    environment:
      MODE: standalone
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    volumes:
      - nacos_data:/home/<USER>/data
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    networks:
      - edu-network

  # Sentinel控制台
  sentinel:
    image: bladex/sentinel-dashboard:1.8.0
    container_name: edu-sentinel
    restart: always
    ports:
      - "8858:8858"
    environment:
      - JAVA_OPTS=-Dserver.port=8858 -Dcsp.sentinel.dashboard.server=localhost:8858 -Dproject.name=sentinel-dashboard
    networks:
      - edu-network

  # Seata分布式事务协调器
  seata:
    image: seataio/seata-server:1.6.0
    container_name: edu-seata
    restart: always
    ports:
      - "7091:7091"
      - "8091:8091"
    volumes:
      - ./seata-config/file.conf:/seata-server/resources/file.conf
      - ./seata-config/registry.conf:/seata-server/resources/registry.conf
    depends_on:
      - mysql
      - nacos
    networks:
      - edu-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: edu-redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass 123456
    volumes:
      - redis_data:/data
    networks:
      - edu-network

volumes:
  mysql_data:
  nacos_data:
  redis_data:

networks:
  edu-network:
    driver: bridge