server:
  port: 8080

spring:
  application:
    name: edu-gateway
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
      config:
        server-addr: localhost:8848
        file-extension: yml
        namespace: public
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        - id: edu-auth-service
          uri: lb://edu-auth-service
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        - id: edu-user-service
          uri: lb://edu-user-service
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1
        - id: edu-course-service
          uri: lb://edu-course-service
          predicates:
            - Path=/course/**
          filters:
            - StripPrefix=1
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      datasource:
        ds1:
          nacos:
            server-addr: localhost:8848
            dataId: gateway-sentinel
            groupId: DEFAULT_GROUP
            rule-type: gw-flow

logging:
  level:
    com.edu: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'